Product Requirements Document (PRD)

Product: Digital Asset Manager (DAM) MVP – “Resize, Convert, Ship.”
Client Context: Helloworld Travel – heavy weekly image resizing (400+/wk) currently done in GIMP; strong need for speed, consistency, and ROI visibility.
Owner: <you/PM>
Stakeholders: Marketing, Web, CX, IT (governance), Legal/Privacy
Status: Draft for review
Date: 2025-08-26
Version: v0.9

1) Background & Problem Statement

Helloworld staff spend significant time hunting, resizing, and exporting images (and other assets) across channels. Manual steps create bottlenecks, inconsistencies (dimensions/weights), and slow page-loads. We will deliver a lightweight DAM MVP that centralizes assets, batch-converts to common formats/sizes, and tracks ROI from day one. The MVP deliberately teases downstream integrations (Salesforce, Zendesk, Microsoft 365, Travefy, Website CDN) without revealing implementation IP.

2) Goals & Non-Goals
Goals (MVP)

Central library & versioning: Upload, preview, tag, and maintain originals + derivatives.

Batch conversions: Resize/crop/rotate; export to JPG/PNG/WebP/AVIF; file-size targets (e.g., “≤ 500 KB”).

Visual controls: In-canvas preview + sliders for width/height/quality and dropdowns for format.

Presets: Create, edit, share, and apply reusable spec bundles (e.g., “1800×618 · JPG · ≤500 KB”).

ROI dashboard: Live counters for throughput, average size reduction, time saved, and $ impact (edit labor rate).

Teasers: Clearly show “next step” value: Website Performance Pack, Salesforce/Zendesk Pack, Office Docs Pack.

Non-Goals (MVP)

No full DRM (rights purchase/contract storage).

No public CDN delivery (we only prepare assets; publishing is a later pack).

No deep DTP or advanced creative tooling (e.g., masks, layers, blend modes).

No proprietary “how we integrate” details—benefits only.

3) Success Metrics (MVP)

Time efficiency: ≥ 50–70% reduction in per-image handling vs. GIMP baseline (measured in-app).

Throughput: 400 images/week processed with <30 min/day operator time.

File weight: ≥30% average reduction on web-targeted derivatives (via WebP/AVIF) at acceptable quality.

Adoption: ≥ 5 active users weekly by end of Sprint 2.

Satisfaction: ≥ 80% thumbs-up on preset usefulness survey.

Commercial signal: At least one follow-on pack approved within 30 days post-MVP.

4) Personas & JTBD

Marisa – Marketing Producer: “Batch 120 hero images to the right spec before campaign goes live—fast and consistent.”

Alex – Web Coordinator: “Deliver responsive, lightweight images that won’t tank Core Web Vitals.”

Sam – CX/Content Agent: “Attach size-correct brochures to tickets without emailing huge files.”

Priya – Brand Manager: “Enforce spec, naming, and expiry rules so everything looks on-brand.”

IT/Privacy: “Ensure access controls, auditability, and safe handling of any customer-related images.”

5) High-Level Scope (MVP Features)
F1. Upload & Library

Single/multi-file upload (drag-drop and button).

Preview grid (thumbnails), sort, filter by tags, format, date, user.

Asset detail panel: preview, metadata, derivatives list, activity log.

F2. Visual Manipulation

Canvas preview with: resize sliders (width/height), maintain aspect toggle, crop, rotate, quality slider, format dropdown (JPG/PNG/WebP/AVIF).

“Generate derivatives” button (primary action) with progress indicator.

F3. Batch Processing

Multi-select in grid; Apply Preset to N items; background job queue; result summary; retry failed.

F4. Presets (Core)

Create/Edit/Delete preset (fields: name, width/height, format, target file size, quality, optional watermark flag).

Preset visibility: Personal vs Team.

Preset versioning (v1, v1.1, etc.) with change note.

F5. Versioning & Derivatives

Always keep original file.

Track each derivative with a link to source, preset used, parameters, and checksum.

F6. Export & Packaging

Download single derivative; Zip package for batch results.

Optional “Copy link” (private, time-limited).

F7. ROI Dashboard (MVP)

KPIs (Today / This week / All-time):

Images processed

Avg. size reduction (%)

Time saved (baseline vs. actual; baseline set at X min/image, editable)

$ saved (labor rate editable)

CSV export of run history for stakeholder updates.

F8. Access & Audit

Roles: Admin, Editor, Viewer.

Audit log: who uploaded/edited/published presets, who downloaded what, when.

F9. Integrations – Teasers (Benefits only)

Website Performance Pack: Serve responsive WebP/AVIF, purge cache on publish.

Salesforce/Zendesk Pack: Attach size-right assets to Opportunities/Cases/Tickets; respect platform limits.

Office Docs Pack: One-click DOCX/PPTX→PDF for brochures/quotes; consistent look.

6) Out of Scope (MVP)

Public asset browsing, external sharing portals.

Advanced rights management (license contracts, cost centers).

Video streaming/transmuxing beyond simple MP4/HLS generation (teased for later).

SSO/SCIM (can be staged in post-MVP).

7) User Stories & Acceptance Criteria (examples)
US-1 Upload & preview

As an Editor, I want to upload multiple images and see them instantly, so that I can start batch work.
Acceptance:

Given I select 50 images, when I upload, then I see a progress bar and thumbnails appear in the grid on completion.

Failed files show error badges with reasons (size/type) and a Retry button.

US-2 Create & apply preset

As a Producer, I want to save a preset for “1800×618 · JPG · ≤500 KB,” so that I can apply it in bulk.
Acceptance:

I can name the preset, set dimensions, format, size cap, and quality.

When I multi-select 100 images and click Apply Preset, a batch job runs and generates derivatives with a success ratio ≥ 95%.

US-3 Visual tooling

As an Editor, I want a preview with sliders and crop, so that I can fine-tune per asset.
Acceptance:

Width/height sliders retain aspect if toggle is on.

Cropping respects minimum dimension requirements; undo/redo is available.

US-4 ROI dashboard

As a PM, I want to see time/$ saved, so that I can justify expansion.
Acceptance:

Baseline minutes/image is editable (default: 2.0).

Dashboard shows images processed, avg. weight reduction, time saved, $ saved; CSV export includes per-run stats.

US-5 Governance & audit

As an Admin, I want to control who can create team presets and download zips, so that we maintain consistency.
Acceptance:

Admin can assign roles; all critical actions are logged with timestamp and user.

8) UX Requirements
Information Architecture

Left: Library (folders/collections), Filters, Bulk actions.

Center: Grid/List, selection state, infinite scroll.

Right: Details/Presets panel toggles.

Top: Global search + KPI mini-cards (Today/This week).

Interaction Patterns

One primary action per screen (e.g., Generate derivatives).

Keyboard shortcuts for power users: select all, apply preset, download.

Batch affordances: persistent selection bar with count and “Apply Preset”.

Accessibility

WCAG 2.1 AA: keyboard focus states, alt text on previews, sufficient contrast, ARIA for sliders.

Error messaging is actionable (what failed + how to fix).

Copy & Tone (microcopy examples)

Empty state: “Drop files here to start. Or pick a folder and Apply Preset to everything.”

Success toast: “78 images optimized. Avg. weight −42%. See report →”

9) Presets Specification

Preset fields (MVP):

name (string), width (px), height (px), keepAspect (bool), format (enum: JPG|PNG|WEBP|AVIF|PDF), quality (0–100), maxFileKB (int), watermark (bool), visibility (Personal|Team), notes (string), version (semver), createdBy, createdAt.

Starter presets (preloaded examples):

Hero Wide: 1800×618 · JPG · ≤ 500 KB

Blog Tile: 1100×500 · JPG · ≤ 500 KB

Gallery HD: 1920×1280 · WebP

Email Thumb: 600×400 · JPG · ≤ 120 KB

Social Square: 1080×1080 · WebP · ≤ 300 KB

Rules:

If maxFileKB is set, encoder will iteratively lower quality until threshold or minimum quality reached (configurable).

Presets are versioned; applying an older version records the version used for reproducibility.

10) Data Model (logical, tech-agnostic)

Entities & Key Fields

Asset: id, originalFilename, mimeType, bytes, dimensions, hash, uploadedBy, uploadedAt, tags[], collections[], status

Derivative: id, assetId, presetId, params{}, mimeType, bytes, dimensions, hash, createdAt, createdBy, status

Preset: as above

Collection/Project: id, name, description, owner, members[], createdAt

User: id, name, email, role (Admin/Editor/Viewer)

Job (batch): id, type, createdBy, createdAt, itemsTotal, itemsDone, itemsFailed, status, metrics{}

AuditEvent: id, actor, action, targetType, targetId, meta{}, timestamp

Search & Filters

By tag, format, uploader, date range, collection, “has derivatives,” “missing preset.”

11) Analytics & Telemetry (events spec)

asset.uploaded (bytes, mime, duration)

preset.created (visibility, version)

batch.started / batch.completed (count, successRate, avgBytesSaved, avgSecsPerItem)

derivative.generated (format, width, height, quality, bytesBefore/After)

dashboard.viewed / dashboard.exported

settings.baseline.changed (old, new)
All events include userId, timestamp, and a session id.

12) Security, Privacy, Compliance

Role-based access (Admin/Editor/Viewer).

Private links by default; time-limited links for downloads.

No PII extraction or storage beyond user accounts; if customer images are uploaded, mark asset sensitivity = high and disable public links.

Audit log immutable for 90 days minimum (configurable).

Data retention policy: originals retained; derivatives can be re-generated (configurable cleanup).

13) Performance & Reliability Targets (MVP)

Batch size: Up to 300 images per job without timeouts.

Throughput: ≥ 5 images/sec average on mid-sized images (assumption doc to capture test fixtures).

Editor latency: Canvas preview updates <150 ms after slider changes (perceptual smoothness).

Availability: Business hours target ≥ 99.5% during pilot (soft SLO).

Error budget: ≤ 2% fail rate per batch, with Retry restoring ≥ 95% overall success.

14) Dependencies & Assumptions

Users provide authentic sample folders (campaign images) for realistic baselines.

Legal confirms acceptable use for watermarking and derivative generation.

IT approves initial role matrix and privacy posture.

We maintain a private implementation runbook (our IP).

15) Milestones & Phasing

Sprint 0 (Planning): Requirements sign-off, sample assets, baseline timing study (GIMP).
Sprint 1: Upload/library, visual editor, single-asset export, presets v1, basic dashboard tiles.
Sprint 2: Batch processing, zip export, preset sharing, ROI dashboard export, audit log, roles.
Pilot Handover (Week 4): KPI review, go/no-go on first Pack.

Go/No-Go Criteria (pilot):

≥ 400 images processed in real work

≥ 50% per-image time reduction

Dashboard evidence exported and accepted by stakeholders

16) UAT Plan (test scenarios)

UAT-1: Upload 500 mixed assets (JPEG/PNG) → zero crashes; bad files reported cleanly.

UAT-2: Apply “Hero Wide” preset to 150 images → ≥ 95% success; ≤ 5% manual tweaks.

UAT-3: Compare 30 images pre/post: average size reduction ≥ 30% at acceptable visual quality (panel review).

UAT-4: Editor can create Team preset; Viewer cannot.

UAT-5: Dashboard export matches batch logs within ±1%.

17) Risks & Mitigations

Risk: Quality vs. file-size tension.

Mitigation: Visual A/B compare, adjustable quality slider, preset versioning.

Risk: Users ignore presets, revert to old habits.

Mitigation: Opinionated defaults; first-run coach marks; shortcut keys.

Risk: Large batches stall.

Mitigation: Background job queue, resumable batches, per-item retry.

Risk: Scope creep into publishing/CDN.

Mitigation: Keep Packs post-MVP; benefit-teasers only.

18) Teaser: Post-MVP Packs (Benefits Only)

Website Performance Pack: Responsive breakpoints + WebP/AVIF + cache purge → faster pages and uplift in conversions.

Salesforce/Zendesk Pack: Right-sized attachments and links → lighter records, fewer ticket bounces.

Office Docs Pack: One-click DOCX/PPTX → PDF for brochures/quotes → consistency and speed.

Governance Pack: License expiry alerts, usage analytics, retention rules.

19) Example Content (ready to drop in)
19.1 Preset Examples (ready-made)

Hero Wide – 1800×618 · JPG · ≤ 500 KB (brand hero images)

Blog Tile – 1100×500 · JPG · ≤ 500 KB (article tops)

Gallery HD – 1920×1280 · WebP (destination album)

Email Thumb – 600×400 · JPG · ≤ 120 KB (EDM modules)

Social Square – 1080×1080 · WebP · ≤ 300 KB (IG/FB)

19.2 Naming Convention (suggested)

<Brand>_<Destination>_<Placement>_<WxH>_<Format>_v<semver>.<ext>
Example: HLO_Tahiti_Hero_1800x618_JPG_v1.0.jpg

19.3 ROI Formula (dashboard)

Baseline mins/image: default 2.0 (editable)

Actual mins/image: measured by operator sessions

Time saved (mins): (baseline − actual) × images_processed

$ saved: time_saved_hours × blended_rate (rate editable)

Weight delta (%): (orig_bytes − new_bytes) / orig_bytes